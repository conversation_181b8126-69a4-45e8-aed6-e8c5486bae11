from paddleocr import PaddleOCR

# 初始化 OCR
ocr = PaddleOCR(
    use_angle_cls=True,  # 方向分类
    lang='ch',           # 中文
    ocr_version='PP-OCRv5'
)

# 识别图片
image_path = '/Users/<USER>/Downloads/999999999999999测试图片.png'
results = ocr.predict(image_path)

# 输出结果
for result in results:
    for line in result:
        text, confidence = line['transcription'], line['score']
        print(f"识别结果: {text}, 置信度: {confidence:.3f}")
